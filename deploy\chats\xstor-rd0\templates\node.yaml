# SPDX-License-Identifier: Apache-2.0
# Copyright (c) Arm Limited and Contributors
# Copyright (c) Intel Corporation
---
kind: DaemonSet
apiVersion: apps/v1
metadata:
  name: xstor-node-rd0
  namespace: {{ .Release.Namespace }}
spec:
  selector:
    matchLabels:
      app: xstor-node-rd0
  template:
    metadata:
      labels:
        app: xstor-node-rd0
    spec:
      hostNetwork: true
      dnsPolicy: ClusterFirstWithHostNet
      containers:
      - name: rd0-tapi
        securityContext:
          privileged: true
          capabilities:
            add: ["SYS_ADMIN"]
          allowPrivilegeEscalation: true
        image: {{ .Values.image.tAPI.repository }}:{{ .Values.image.tAPI.tag }}
        ports:  
        - containerPort: {{ .Values.config.tAPIPort }}
        command: ["bash", "-x", "/root/start-tapi.sh", {{ quote .Values.config.backendType }}]
        imagePullPolicy: "Always"
        env:
        - name: Transport
          value: {{ .Values.config.transport }}
        - name: Protocol
          value: {{ .Values.config.protocol }}
        - name: XTGTPort
          value: {{ quote .Values.config.xTGTPort }}
        - name: TAPIPort
          value: {{ quote .Values.config.tAPIPort }}
        - name: PoolName
          value: {{ .Values.config.poolName }}
        - name: APIAuth
          valueFrom:  
            secretKeyRef:  
              name: rd0-tapi-auth
              key: API_AUTH
        - name: NodeName
          valueFrom:
            fieldRef:
              fieldPath: spec.nodeName
        - name: NodeHostIP
          valueFrom:
            fieldRef:
              fieldPath: status.hostIP
        - name: LC_ALL
          value: en_US.UTF-8
        - name: MGRAddr
          value: {{ .Values.config.mgrAddr }}
        {{- if .Values.config.multiRD1Clusters }}
        - name: MultiRD1Clusters
          value: {{ quote .Values.config.multiRD1Clusters }}
        {{- end }}
        - name: TZ
          value: "Asia/Shanghai"
        volumeMounts:
        - name: host-dev
          mountPath: /dev
        {{- if and (eq .Values.config.protocol "nvmf") (eq .Values.config.backendType "rbd") }}
        - name: opt-nvmf-target
          mountPath: /opt/nvmf_target
        {{- end }}
        - name: rpc-socket-dir
          mountPath: /var/tmp/
        - name: tapi-start
          mountPath: /root/start-tapi.sh
          subPath: start-tapi.sh
        {{- if eq .Values.config.backendType "rbd" }}
        - name: ceph-config
          mountPath: /etc/ceph/ceph.conf
          subPath: ceph.conf
        - name: ceph-keyring
          mountPath: /etc/ceph/ceph.client.admin.keyring
          subPath: keyring
        {{- end }}
        - name: initiator-config
          mountPath: /etc/iscsi
        - name: hostnqn-config
          mountPath: /etc/nvme
        - name: rd0-cover-systemctl
          mountPath: /usr/bin/systemctl
          subPath: systemctl
      - name: rd0-tgw
        securityContext:
          privileged: true
          capabilities:
            add: ["SYS_ADMIN"]
          allowPrivilegeEscalation: true
        image: {{ .Values.image.tAPI.repository }}:{{ .Values.image.tAPI.tag }}
        command: ["bash", "-x", "/root/start-tgw.sh", {{ quote .Values.config.backendType }}]
        imagePullPolicy: "Always"
        env:
        - name: Transport
          value: {{ .Values.config.transport }}
        - name: Protocol
          value: {{ .Values.config.protocol }}
        - name: XTGTPort
          value: {{ quote .Values.config.xTGTPort }}
        - name: TAPIPort
          value: {{ quote .Values.config.tAPIPort }}
        - name: PoolName
          value: {{ .Values.config.poolName }}
        - name: APIAuth
          valueFrom:  
            secretKeyRef:  
              name: rd0-tapi-auth
              key: API_AUTH
        - name: NodeName
          valueFrom:
            fieldRef:
              fieldPath: spec.nodeName
        - name: NodeHostIP
          valueFrom:
            fieldRef:
              fieldPath: status.hostIP
        - name: LC_ALL
          value: en_US.UTF-8
        - name: MGRAddr
          value: {{ .Values.config.mgrAddr }}
        {{- if .Values.config.multiRD1Clusters }}
        - name: MultiRD1Clusters
          value: {{ quote .Values.config.multiRD1Clusters }}
        {{- end }}
        - name: TZ
          value: "Asia/Shanghai"
        volumeMounts:
        - name: host-dev
          mountPath: /dev
        {{- if and (eq .Values.config.protocol "nvmf") (eq .Values.config.backendType "rbd") }}
        - name: opt-nvmf-target
          mountPath: /opt/nvmf_target
        {{- end }}
        - name: rpc-socket-dir
          mountPath: /var/tmp/
        - name: tgw-start
          mountPath: /root/start-tgw.sh
          subPath: start-tgw.sh
        {{- if eq .Values.config.backendType "rbd" }}
        - name: ceph-config
          mountPath: /etc/ceph/ceph.conf
          subPath: ceph.conf
        - name: ceph-keyring
          mountPath: /etc/ceph/ceph.client.admin.keyring
          subPath: keyring
        {{- end }}
        - name: initiator-config
          mountPath: /etc/iscsi
        - name: hostnqn-config
          mountPath: /etc/nvme
      - name: rd0-xtgt
        securityContext:
          privileged: true
          capabilities:
            add: ["SYS_ADMIN"]
          allowPrivilegeEscalation: true
        image: {{ .Values.image.xTGT.repository }}:{{ .Values.image.xTGT.tag }}
        ports:  
        - containerPort: {{ .Values.config.xTGTPort }}
        resources:
          requests:
            memory: 12Gi
            cpu: 4
        command: ["bash", "-x", "/root/start-xtgt.sh", {{ quote .Values.config.backendType }}]
        imagePullPolicy: "Always"
        env:
        - name: MGRAddr
          value: {{ .Values.config.mgrAddr }}
        - name: NodeHostIP
          valueFrom:
            fieldRef:
              fieldPath: status.hostIP
        - name: TAPIPort
          value: {{ quote .Values.config.tAPIPort }}
        - name: APIAuth
          valueFrom:
            secretKeyRef:
              name: rd0-tapi-auth
              key: API_AUTH
        - name: TZ
          value: "Asia/Shanghai"
        volumeMounts:
        - name: host-dev
          mountPath: /dev
        - name: dev-shm
          mountPath: /dev/shm
        - name: rpc-socket-dir
          mountPath: /var/tmp/
        - name: xtgt-start
          mountPath: /root/start-xtgt.sh
          subPath: start-xtgt.sh
        - name: restore-config
          mountPath: /root/config_parser.py
          subPath: config_parser.py
        {{- if and (eq .Values.config.protocol "nvmf") (eq .Values.config.backendType "rbd") }}
        - name: opt-nvmf-target
          mountPath: /opt/nvmf_target
        {{- end }}
        {{- if eq .Values.config.backendType "rbd" }}
        - name: ceph-config
          mountPath: /etc/ceph/ceph.conf
          subPath: ceph.conf
        - name: ceph-keyring
          mountPath: /etc/ceph/ceph.client.admin.keyring
          subPath: keyring
        {{- end }}
      - name: rd0-wsproxy
        image: {{ .Values.image.wsProxy.repository }}:{{ .Values.image.wsProxy.tag }}
        command: ["bash", "-x", "/root/start-wsproxy.sh", {{ quote .Values.config.backendType }}]
        imagePullPolicy: "Always"
        env:
        - name: MGRAddr
          value: {{ .Values.config.mgrAddr }}
        - name: NodeHostIP
          valueFrom:
            fieldRef:
              fieldPath: status.hostIP
        - name: TAPIPort
          value: {{ quote .Values.config.tAPIPort }}
        - name: TZ
          value: "Asia/Shanghai"
        volumeMounts:
        - name: wsproxy-start
          mountPath: /root/start-wsproxy.sh
          subPath: start-wsproxy.sh
      volumes:
      - name: host-dev
        hostPath:
          path: /dev
      {{- if and (eq .Values.config.protocol "nvmf") (eq .Values.config.backendType "rbd") }}
      - name: opt-nvmf-target
        hostPath:
          path: /opt/nvmf_target
      {{- end }}
      - name: dev-shm
        emptyDir:
          medium: Memory
      - name: rpc-socket-dir
        emptyDir: {}
      - name: tapi-start
        configMap:
          name: rd0-start-tapi
          defaultMode: 0755
      - name: tgw-start
        configMap:
          name: rd0-start-tgw
          defaultMode: 0755
      - name: restore-config
        configMap:
          name: rd0-restore-config
          defaultMode: 0755
      - name: xtgt-start
        configMap:
          name: rd0-start-xtgt
          defaultMode: 0755
      - name: wsproxy-start
        configMap:
          name: rd0-start-wsproxy
          defaultMode: 0755
      - name: initiator-config
        hostPath:
          path: /etc/iscsi/
          type: DirectoryOrCreate
      - name: hostnqn-config
        hostPath:
          path: /etc/nvme/
          type: DirectoryOrCreate
      {{- if eq .Values.config.backendType "rbd" }}
      - name: ceph-config
        configMap:
          name: ceph-config
      - name: ceph-keyring
        secret:
          secretName: ceph-client-admin-keyring  
          items:  
          - key: keyring  
            path: keyring
      {{- end }}
      - name: rd0-cover-systemctl
        configMap:
          name: rd0-cover-systemctl
          defaultMode: 0755
      {{- with .Values.rd0Nodes }}
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                  - key: kubernetes.io/hostname
                    operator: In
                    values:
      {{- toYaml . | nindent 22 }}
      {{- end }}