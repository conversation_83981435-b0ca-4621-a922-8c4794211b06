#!/bin/bash

set -e
set -x
export TZ=Asia/Shanghai
source /root/utils
RBDTAPIConfig=/etc/ceph/iscsi-gateway.cfg
NOFTAPIConfig=/opt/xstor_tgt/nvmf.cfg
XStorTAPIConfig=/opt/xstor_tgt/iscsi-gateway.cfg
BitmapFileDir=/opt/xstor_tgt/bitmapfile/


function aes_encode_userpwd()
{
    userpwd=$1
    output=$(python3 - "$userpwd" <<END
from xstor_config.s3_oper import AES_helper
import sys
pwd=sys.argv[1]
print(AES_helper.aes_encode(pwd))
END
    )
    echo $output
}

render_rbd_tapi_config() {
    ensure_env_var NodeHostIP
    ensure_env_var NodeName
    ensure_env_var Transport
    ensure_env_var Protocol
    ensure_env_var XTGTPort
    ensure_env_var TAPIPort
    ensure_env_var APIAuth
    ensure_env_var PoolName

    HostName=$(hostname)
    api_user=`echo $APIAuth | awk -F "/" '{print $1}'`
    api_password=`echo $APIAuth | awk -F "/" '{print $2}'`

    cat>"${RBDTAPIConfig}"<< EOF
[config]
cluster_name = ceph
gateway_keyring = ceph.client.admin.keyring
api_secure = false
api_user = $api_user
api_password = $api_password
pool = $PoolName
gateway_conf = ${HostName}-gateway.conf
cluster_user_name = client.admin
api_port = ${TAPIPort}
snapshot_file_dir = /tmp/
trusted_ip_list = 0.0.0.0/0
backup_export_mode = slice
multi_cluster= False
export_size= 1G
speed_limit = 100M
gateways_list = ${HostName}/${NodeHostIP}
minimum_gateways = 1
transport = $Transport
protocol = $Protocol
nvmf_listener_addr = $NodeHostIP
nvmf_listener_port = $XTGTPort
EOF
}

render_xstor_tapi_config() {
    ensure_env_var APIAuth
    ensure_env_var MGRAddr
    ensure_env_var NodeHostIP
    ensure_env_var NodeName

    api_user=`echo $APIAuth | awk -F "/" '{print $1}'`
    api_password=`echo $APIAuth | awk -F "/" '{print $2}'`
    api_version=$(aes_encode_userpwd ${api_password})
    # remove port from MGRAddr
    MasterIPGroup=`echo $MGRAddr | sed 's/:[0-9]*//g'`
    IFS=',' read -r -a addrs <<< "$MasterIPGroup"
    for TmpAddr in "${addrs[@]}"; do
        # check if TmpAddr is a domain, if so, resolve it into IP
        if [[ $TmpAddr =~ [a-zA-Z] ]]; then
            ## Begin retrieve MasterIPGroup
            log_info "$TmpAddr is a domain"
            max_attempts=90
            sleep_interval=1
            for attempt in $(seq 1 $max_attempts); do
                log_info "Resolving domain $TmpAddr ..."
                IPAddr=$(get_ips_by_domains $TmpAddr)
                log_info "Attempt $attempt: TmpAddr-------->"$TmpAddr
                if [ -n "$IPAddr" ]; then
                    MasterIPGroup=$(echo $MasterIPGroup | sed "s/$TmpAddr/$IPAddr/g")
                    break
                else
                    log_warning "Attempt $attempt failed to retrieve TmpAddr, retrying in $sleep_interval seconds..."
                    sleep $sleep_interval
                fi
            done
            ## Begin retrieve MasterIPGroup
        fi
    done


    if [ -z "$MasterIPGroup" ]; then
        log_error "MasterIPGroup is None!"
        exit -1
    fi
    ## End of retry loop to retrieve MasterIPGroup

    cat>"${XStorTAPIConfig}"<< EOF
[config]
# Name of the light storage cluster.
cluster_name = light
api_secure = false 
# Additional API configuration options are as follows, defaults shown.
api_mark = $api_user
api_version = $api_version
# mgr port
# api_port = 5000
# target api port
inner_port = ${TAPIPort}
snapshot_file_dir = /var/tmp/
object_size = 1G
export_size = 1G
disk_mgr_type = zfs
trusted_ip_list = 0.0.0.0/0
# vip ip地址, 已弃用
# vip_addr = $vip_addr
master_ip_group = ${MasterIPGroup}
bitmap_file_dir = $BitmapFileDir
node_name = $NodeName
node_address = $NodeHostIP
tgt_port_list=3260
EOF

    if [ -n "$MultiRD1Clusters" ]; then
        # Remove leading spaces from all lines and write to config
        echo "$MultiRD1Clusters" | sed 's/^[[:space:]]*//' >> ${XStorTAPIConfig}
    fi
}

start_rbd_tapi() {
    python3 /usr/bin/rbd-target-api
    if [ $? -ne 0 ]; then
        log_error "start_rbd_tapi failed."
        exit -1
    fi
}
start_xstor_tgw() {
    python3 /usr/bin/xstor-targetgw
    if [ $? -ne 0 ]; then
        log_error "start_xstor_tgw failed."
        exit -1
    fi
}
main() {
    if [ ! -z "$1" ] && [ "$1" == "rbd" ]; then
        log_info "Rendering rbd-target-api config..."
        render_rbd_tapi_config
        # log_info "Initializing nof config directory..."
        # mkdir -p $(dirname ${NOFTAPIConfig})
        log_info "Starting rbd-target-api..."
        start_rbd_tapi
    elif [ "$1" == "light" ]; then
        log_info "Rendering xstor-targetgw config..."
        render_xstor_tapi_config
        log_info "Starting xstor-targetgw..."
        start_xstor_tgw
    fi
}
main "$@"
